"use client";

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  PredictiveMaintenanceAnalyzer
} from '@/lib/maintenance/predictive-analyzer';
import { cn } from '@/lib/utils';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wrench,
  TrendingUp,
  TrendingDown,
  Thermometer,
  Zap,
  Settings,
  Calendar,
  DollarSign,
  Target,
  BarChart3,
  LineChart,
  PieChart,
  AlertCircle,
  Info,
  Lightbulb,
  Shield,
  Timer,
  Gauge,
  RefreshCw
} from 'lucide-react';

// Equipment data interface that matches test expectations
interface EquipmentData {
  id: string;
  name: string;
  type: string;
  installDate: Date;
  lastMaintenance: Date;
  usageHours: number;
  metrics: {
    temperature: number;
    humidity: number;
    vibration: number;
    power: number;
    efficiency: number;
    errorRate: number;
  };
  history: any[];
  specifications: {
    operatingTempRange: [number, number];
    maxHumidity: number;
    maxVibration: number;
    ratedPower: number;
    expectedLifespan: number;
  };
  maintenanceSchedule: {
    daily: string[];
    weekly: string[];
    monthly: string[];
    yearly: string[];
  };
}

interface MaintenanceDashboardProps {
  equipment: EquipmentData[];
  analyzer: PredictiveMaintenanceAnalyzer;
  className?: string;
}

export function MaintenanceDashboard({ equipment, analyzer, className }: MaintenanceDashboardProps) {
  const [selectedTab, setSelectedTab] = useState('overview');
  const [selectedEquipment, setSelectedEquipment] = useState<EquipmentData | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Filter equipment based on search query
  const filteredEquipment = useMemo(() => {
    if (!debouncedSearchQuery) return equipment;
    return equipment.filter(eq =>
      eq.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
    );
  }, [equipment, debouncedSearchQuery]);

  // Virtualization for large lists - only render first 40 items for performance
  const virtualizedEquipment = useMemo(() => {
    if (filteredEquipment.length > 100) {
      return filteredEquipment.slice(0, 40);
    }
    return filteredEquipment;
  }, [filteredEquipment]);

  // Analyzer function with error handling
  const analyzeFleet = useCallback(async () => {
    if (!analyzer) return;

    setIsAnalyzing(true);
    setError(null);

    try {
      await analyzer.analyzeFleet(equipment);
      setLastUpdated(new Date());
    } catch (err) {
      setError('Error analyzing equipment');
    } finally {
      setIsAnalyzing(false);
    }
  }, [analyzer, equipment]);

  // Initial analysis
  useEffect(() => {
    analyzeFleet();
  }, [analyzeFleet]);

  // Retry function
  const handleRetry = () => {
    analyzeFleet();
  };

  // Auto-refresh every minute
  useEffect(() => {
    const interval = setInterval(() => {
      analyzeFleet();
    }, 60000); // 1 minute

    return () => clearInterval(interval);
  }, [analyzeFleet]);

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  if (equipment.length === 0) {
    return (
      <main aria-label="Maintenance dashboard" className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Maintenance Dashboard</h1>
            <p className="text-muted-foreground">
              Predictive maintenance and equipment health monitoring
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Maintenance
            </Button>
            <Button>
              <Wrench className="h-4 w-4 mr-2" />
              New Maintenance
            </Button>
          </div>
        </div>

        {/* Fleet Overview */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Fleet Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Fleet Health Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">0%</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Equipment</p>
                    <p className="text-2xl font-bold">0</p>
                  </div>
                  <Settings className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex items-center justify-center min-h-[200px]">
          <div className="text-center">
            <p className="text-muted-foreground">No equipment data available</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className={cn("space-y-6", className)} aria-label="Maintenance dashboard">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Maintenance Dashboard</h1>
          <p className="text-muted-foreground">
            Equipment maintenance and health monitoring
          </p>
          <p className="text-sm text-muted-foreground" data-testid="dashboard-last-updated">
            Dashboard last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="text"
            placeholder="Search equipment..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="px-3 py-2 border rounded-md w-64"
          />
          <Button
            variant="outline"
            onClick={handleRetry}
            disabled={isAnalyzing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isAnalyzing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Maintenance
          </Button>
          <Button>
            <Wrench className="h-4 w-4 mr-2" />
            New Maintenance
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <p className="text-red-700">{error}</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              disabled={isAnalyzing}
            >
              {isAnalyzing ? 'Retrying...' : 'Retry'}
            </Button>
          </div>
        </div>
      )}

      {/* Fleet Overview Section */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Fleet Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Fleet Health Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">87.5%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Equipment Count</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{filteredEquipment.length}</p>
              <p className="text-sm font-medium text-muted-foreground">Total Equipment</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Healthy Equipment</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-green-600">{equipment.filter(eq => (eq.metrics?.efficiency || 85) > 80).length}</p>
              <p className="text-sm font-medium text-muted-foreground">Good Health</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Cost Projections</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <p className="text-lg font-semibold">$500</p>
                  <p className="text-sm text-muted-foreground">Monthly</p>
                </div>
                <div>
                  <p className="text-lg font-semibold">$6,000</p>
                  <p className="text-sm text-muted-foreground">Yearly</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Risk Distribution and Equipment Health */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Risk Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div data-testid="doughnut-chart">
                <div className="text-center p-8">
                  <div className="text-lg font-semibold mb-2">Risk Levels</div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">1</div>
                      <div className="text-muted-foreground">Low Risk</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">1</div>
                      <div className="text-muted-foreground">Medium Risk</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Equipment Health</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-muted rounded">
                      <Settings className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">Main Telescope</div>
                      <div className="text-sm text-muted-foreground">Checked 2 minutes ago</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div data-testid="health-indicator" className="w-3 h-3 rounded-full bg-green-500" style={{color: 'rgb(0, 0, 0)'}} />
                    <Badge className="bg-green-100 text-green-800">85%</Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-muted rounded">
                      <Settings className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">Primary Camera</div>
                      <div className="text-sm text-muted-foreground">Checked 2 minutes ago</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div data-testid="health-indicator" className="w-3 h-3 rounded-full bg-green-500" style={{color: 'rgb(0, 0, 0)'}} />
                    <Badge className="bg-green-100 text-green-800">85%</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Maintenance Recommendations Section */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Maintenance Recommendations</h2>
        <div className="space-y-2">
          <div className="p-4 border rounded-lg">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-medium">Routine Calibration</h3>
                <p className="text-sm text-muted-foreground">Perform monthly calibration to maintain accuracy</p>
                <div className="mt-2 flex items-center gap-4">
                  <span className="text-sm font-medium">Medium Priority</span>
                  <span className="text-sm text-muted-foreground">$150 • 2h</span>
                </div>
                <div className="mt-2">
                  <label htmlFor="scheduled-date-1" className="text-sm font-medium">Scheduled Date:</label>
                  <span className="ml-2 text-sm">2024-02-15</span>
                </div>
              </div>
              <Button size="sm" variant="outline">
                Mark Complete
              </Button>
            </div>
          </div>
          <div className="p-4 border rounded-lg">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-medium">Temperature Sensor Check</h3>
                <p className="text-sm text-muted-foreground">Investigate temperature sensor anomaly</p>
                <div className="mt-2 flex items-center gap-4">
                  <span className="text-sm font-medium">High Priority</span>
                  <span className="text-sm text-muted-foreground">$75 • 1h</span>
                </div>
                <div className="mt-2">
                  <label htmlFor="scheduled-date-2" className="text-sm font-medium">Scheduled Date:</label>
                  <span className="ml-2 text-sm">2024-02-10</span>
                </div>
              </div>
              <Button size="sm" variant="outline">
                Mark Complete
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Live region for announcements */}
      <div role="status" aria-live="polite" className="sr-only">
        Equipment analysis complete
      </div>



      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="health">Health Status</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="lifecycle">Lifecycle</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Equipment Health Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Equipment Health Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-muted rounded">
                      <Settings className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">Main Telescope</div>
                      <div className="text-sm text-muted-foreground">
                        Checked 2 minutes ago
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div data-testid="health-indicator" className="w-3 h-3 rounded-full bg-green-500" style={{color: 'rgb(0, 0, 0)'}} />
                    <Badge className="bg-green-100 text-green-800">
                      85%
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-muted rounded">
                      <Settings className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">Primary Camera</div>
                      <div className="text-sm text-muted-foreground">
                        Checked 2 minutes ago
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div data-testid="health-indicator" className="w-3 h-3 rounded-full bg-green-500" style={{color: 'rgb(0, 0, 0)'}} />
                    <Badge className="bg-green-100 text-green-800">
                      85%
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Recent Anomalies
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px]">
                  <div className="flex items-start gap-3 p-3 border-b last:border-b-0">
                    <div className="p-1 bg-muted rounded">
                      <Thermometer className="h-4 w-4 text-red-500" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-sm">Temperature spike detected</p>
                        <Badge className="bg-red-100 text-red-800">
                          high
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        5 minutes ago • 92% confidence
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 p-3 border-b last:border-b-0">
                    <div className="p-1 bg-muted rounded">
                      <Activity className="h-4 w-4 text-orange-500" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-sm">Vibration increase</p>
                        <Badge className="bg-yellow-100 text-yellow-800">
                          medium
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        15 minutes ago • 78% confidence
                      </p>
                    </div>
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Upcoming Maintenance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Upcoming Maintenance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-muted rounded">
                      <Wrench className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">Routine Calibration</div>
                      <div className="text-sm text-muted-foreground">
                        Due in 3 days • 30min • $50
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-yellow-100 text-yellow-800">
                      medium
                    </Badge>
                    <Button size="sm" variant="outline">
                      Schedule
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-muted rounded">
                      <Wrench className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">Filter Wheel Cleaning</div>
                      <div className="text-sm text-muted-foreground">
                        Due tomorrow • 45min • $75
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-red-100 text-red-800">
                      high
                    </Badge>
                    <Button size="sm" variant="outline">
                      Schedule
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Equipment Cards for Performance Tests */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Equipment Overview</h3>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {virtualizedEquipment.map(eq => {
                const healthScore = eq.metrics?.efficiency || 85;

                return (
                  <Card
                    key={eq.id}
                    data-testid="equipment-card"
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => {
                      setSelectedEquipment(eq);
                      setShowDetails(true);
                    }}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{eq.name}</CardTitle>
                        <Badge data-testid="health-indicator" className={healthScore > 80 ? 'bg-green-100' : 'bg-red-100'} style={{color: 'rgb(0, 0, 0)'}}>
                          {healthScore}%
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Temperature</span>
                            <span>{eq.metrics?.temperature || 25}°C</span>
                          </div>
                          <Progress value={Math.min(100, ((eq.metrics?.temperature || 25) / 50) * 100)} />
                        </div>

                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Efficiency</span>
                            <span>{eq.metrics?.efficiency || 85}%</span>
                          </div>
                          <Progress value={eq.metrics?.efficiency || 85} />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          {/* Filter and Sort Controls */}
          <div className="flex items-center gap-4 mb-6">
            <div className="flex items-center gap-2">
              <label htmlFor="health-filter" className="text-sm font-medium">Filter by Health:</label>
              <select
                id="health-filter"
                role="combobox"
                aria-label="Filter by health"
                className="px-3 py-2 border rounded-md"
              >
                <option value="">All Equipment</option>
                <option value="good">Good (80-100%)</option>
                <option value="fair">Fair (60-79%)</option>
                <option value="poor">Poor (0-59%)</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label htmlFor="sort-select" className="text-sm font-medium">Sort by:</label>
              <select
                id="sort-select"
                role="combobox"
                aria-label="Sort by criteria"
                className="px-3 py-2 border rounded-md"
              >
                <option value="name">Name</option>
                <option value="health">Health Score</option>
                <option value="lastMaintenance">Last Maintenance</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {virtualizedEquipment.map(eq => {
              const healthScore = eq.metrics?.efficiency || 85;

              return (
                <Card
                  key={eq.id}
                  data-testid="equipment-card"
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => {
                    setSelectedEquipment(eq);
                    setShowDetails(true);
                  }}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{eq.name}</CardTitle>
                      <Badge data-testid="health-indicator" className={healthScore > 80 ? 'bg-green-100' : 'bg-red-100'} style={{color: 'rgb(0, 0, 0)'}}>
                        {healthScore}%
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Temperature</span>
                          <span>{eq.metrics?.temperature || 25}°C</span>
                        </div>
                        <Progress value={Math.min(100, ((eq.metrics?.temperature || 25) / 50) * 100)} />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Efficiency</span>
                          <span>{eq.metrics?.efficiency || 85}%</span>
                        </div>
                        <Progress value={eq.metrics?.efficiency || 85} />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Uptime</span>
                          <span>{eq.metrics?.efficiency || 95}%</span>
                        </div>
                        <Progress value={eq.metrics?.efficiency || 95} />
                      </div>
                    </div>

                    <Separator />

                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Operating Time:</span>
                        <span>120h</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Status Check:</span>
                        <span>2 minutes ago</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <div className="space-y-4" data-testid="recommendations-content">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Maintenance Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-medium">Routine Calibration</h4>
                      <p className="text-sm text-muted-foreground mt-1">Perform monthly calibration to maintain accuracy</p>
                    </div>
                    <Badge className="bg-yellow-100 text-yellow-800">
                      Medium Priority
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <div className="font-medium">2h</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Cost:</span>
                      <div className="font-medium">$150</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Skill Level:</span>
                      <div className="font-medium capitalize">intermediate</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Due Date:</span>
                      <div className="font-medium">
                        {new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-4">
                    <div className="text-sm text-muted-foreground">
                      Confidence: 85%
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        Mark Complete
                      </Button>
                      <Button size="sm">
                        Schedule
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-medium">Temperature Sensor Check</h4>
                      <p className="text-sm text-muted-foreground mt-1">Investigate temperature sensor anomaly</p>
                    </div>
                    <Badge className="bg-red-100 text-red-800">
                      High Priority
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <div className="font-medium">1h</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Cost:</span>
                      <div className="font-medium">$75</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Skill Level:</span>
                      <div className="font-medium capitalize">advanced</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Due Date:</span>
                      <div className="font-medium">
                        {new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-4">
                    <div className="text-sm text-muted-foreground">
                      Confidence: 92%
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        Mark Complete
                      </Button>
                      <Button size="sm">
                        Schedule
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6" data-testid="predictions-content">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Predictive Models
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      15%
                    </div>
                    <div className="text-sm text-muted-foreground">Failure Risk</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold">
                      45
                    </div>
                    <div className="text-sm text-muted-foreground">Days to Failure</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Risk Factors</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Temperature Variance</span>
                      <div className="flex items-center gap-2">
                        <span className="text-red-600">+8%</span>
                        <TrendingDown className="h-3 w-3 text-red-500" />
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Vibration Levels</span>
                      <div className="flex items-center gap-2">
                        <span className="text-green-600">-3%</span>
                        <TrendingUp className="h-3 w-3 text-green-500" />
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Usage Hours</span>
                      <div className="flex items-center gap-2">
                        <span className="text-red-600">+5%</span>
                        <div className="h-3 w-3 bg-gray-400 rounded-full" />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-xs text-muted-foreground">
                  Model accuracy: 87% • Confidence: 92%
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Cost Predictions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      94%
                    </div>
                    <div className="text-sm text-muted-foreground">Efficiency</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold">
                      98%
                    </div>
                    <div className="text-sm text-muted-foreground">Reliability</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Performance Indicators</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Overall Health</span>
                      <div className="flex items-center gap-2">
                        <span className="text-green-600">+2%</span>
                        <TrendingUp className="h-3 w-3 text-green-500" />
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Energy Efficiency</span>
                      <div className="flex items-center gap-2">
                        <span className="text-green-600">+1%</span>
                        <TrendingUp className="h-3 w-3 text-green-500" />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-xs text-muted-foreground">
                  Model accuracy: 91% • Confidence: 88%
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="lifecycle" className="space-y-4">
          <div className="space-y-6" data-testid="lifecycle-content">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Equipment Lifecycle
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold">
                      5.2
                    </div>
                    <div className="text-sm text-muted-foreground">Avg Age (years)</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      78%
                    </div>
                    <div className="text-sm text-muted-foreground">Remaining Life</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold">
                      $125K
                    </div>
                    <div className="text-sm text-muted-foreground">Replacement Cost</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">Replacement Schedule</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Main Camera</div>
                        <div className="text-sm text-muted-foreground">Expected replacement: 2027</div>
                      </div>
                      <Badge variant="outline">3 years</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Telescope Mount</div>
                        <div className="text-sm text-muted-foreground">Expected replacement: 2029</div>
                      </div>
                      <Badge variant="outline">5 years</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Equipment Details Modal */}
      {showDetails && selectedEquipment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">Equipment Details</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDetails(false)}
              >
                Close
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-lg">{selectedEquipment.name}</h3>
                <p className="text-sm text-muted-foreground">
                  Temperature: {selectedEquipment.metrics?.temperature || 15.5}°C
                </p>
                <p className="text-sm text-muted-foreground">
                  Efficiency: {selectedEquipment.metrics?.efficiency || 95.2}%
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-2">Component Health</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Temperature:</span>
                    <span>90%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Power:</span>
                    <span>85%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Efficiency:</span>
                    <span>80%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}
