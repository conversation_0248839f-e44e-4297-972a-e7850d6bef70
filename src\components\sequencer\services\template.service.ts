import { SequenceTemplate, Sequence } from '../types/sequencer.types';
import { generateId } from '../utils/sequencer.utils';

export class TemplateService {
  static getBuiltInTemplates(): SequenceTemplate[] {
    return [
      {
        id: 'template-dso-lrgb',
        name: 'Deep Sky LRGB',
        description: 'Complete LRGB imaging sequence for deep sky objects',
        category: 'Deep Sky',
        isBuiltIn: true,
        metadata: {
          tags: ['LRGB', 'Deep Sky', 'Color'],
          difficulty: 'intermediate',
          equipment: ['Camera', 'Filter Wheel', 'Mount'],
          estimatedTime: 4 * 3600, // 4 hours
          targetType: 'dso',
        },
        steps: [
          {
            type: 'slew',
            name: 'Slew to Target',
            description: 'Point telescope at target coordinates',
            duration: 30,
            enabled: true,
            retryCount: 0,
            maxRetries: 3,
            settings: {
              ra: '00h 42m 44s',
              dec: '+41° 16\' 09"',
              targetName: 'M31 - Andromeda Galaxy',
              platesolve: true,
              centerTarget: true,
            },
          },
          {
            type: 'focus',
            name: 'Auto Focus',
            description: 'Perform initial auto focus routine',
            duration: 60,
            enabled: true,
            retryCount: 0,
            maxRetries: 3,
            settings: {
              type: 'auto',
              tolerance: 0.5,
              maxAttempts: 5,
              useTemperatureCompensation: true,
            },
          },
          {
            type: 'filter',
            name: 'Set Luminance Filter',
            description: 'Change to luminance filter',
            duration: 10,
            enabled: true,
            retryCount: 0,
            maxRetries: 3,
            settings: {
              position: 1,
              name: 'Luminance',
              waitTime: 5,
            },
          },
          {
            type: 'capture',
            name: 'Luminance Frames',
            description: 'Capture luminance frames',
            duration: 6000, // 20 x 300s
            enabled: true,
            retryCount: 0,
            maxRetries: 2,
            settings: {
              exposure: 300,
              count: 20,
              binning: '1x1',
              frameType: 'light',
              dither: true,
              ditherPixels: 3,
            },
          },
          {
            type: 'filter',
            name: 'Set Red Filter',
            description: 'Change to red filter',
            duration: 10,
            enabled: true,
            retryCount: 0,
            maxRetries: 3,
            settings: {
              position: 2,
              name: 'Red',
              waitTime: 5,
            },
          },
          {
            type: 'capture',
            name: 'Red Frames',
            description: 'Capture red color frames',
            duration: 3000, // 10 x 300s
            enabled: true,
            retryCount: 0,
            maxRetries: 2,
            settings: {
              exposure: 300,
              count: 10,
              binning: '1x1',
              frameType: 'light',
              dither: true,
              ditherPixels: 3,
            },
          },
          {
            type: 'filter',
            name: 'Set Green Filter',
            description: 'Change to green filter',
            duration: 10,
            enabled: true,
            retryCount: 0,
            maxRetries: 3,
            settings: {
              position: 3,
              name: 'Green',
              waitTime: 5,
            },
          },
          {
            type: 'capture',
            name: 'Green Frames',
            description: 'Capture green color frames',
            duration: 3000, // 10 x 300s
            enabled: true,
            retryCount: 0,
            maxRetries: 2,
            settings: {
              exposure: 300,
              count: 10,
              binning: '1x1',
              frameType: 'light',
              dither: true,
              ditherPixels: 3,
            },
          },
          {
            type: 'filter',
            name: 'Set Blue Filter',
            description: 'Change to blue filter',
            duration: 10,
            enabled: true,
            retryCount: 0,
            maxRetries: 3,
            settings: {
              position: 4,
              name: 'Blue',
              waitTime: 5,
            },
          },
          {
            type: 'capture',
            name: 'Blue Frames',
            description: 'Capture blue color frames',
            duration: 3000, // 10 x 300s
            enabled: true,
            retryCount: 0,
            maxRetries: 2,
            settings: {
              exposure: 300,
              count: 10,
              binning: '1x1',
              frameType: 'light',
              dither: true,
              ditherPixels: 3,
            },
          },
        ],
      },
      {
        id: 'template-planetary',
        name: 'Planetary Imaging',
        description: 'High frame rate planetary imaging sequence',
        category: 'Planetary',
        isBuiltIn: true,
        metadata: {
          tags: ['Planetary', 'High Speed', 'Video'],
          difficulty: 'beginner',
          equipment: ['Camera', 'Mount'],
          estimatedTime: 30 * 60, // 30 minutes
          targetType: 'planet',
        },
        steps: [
          {
            type: 'slew',
            name: 'Slew to Planet',
            description: 'Point telescope at planetary target',
            duration: 30,
            enabled: true,
            retryCount: 0,
            maxRetries: 3,
            settings: {
              ra: '12h 30m 00s',
              dec: '+15° 00\' 00"',
              targetName: 'Jupiter',
              platesolve: false,
              centerTarget: true,
            },
          },
          {
            type: 'focus',
            name: 'Fine Focus',
            description: 'Achieve precise focus for planetary imaging',
            duration: 120,
            enabled: true,
            retryCount: 0,
            maxRetries: 5,
            settings: {
              type: 'auto',
              tolerance: 0.2,
              maxAttempts: 10,
            },
          },
          {
            type: 'capture',
            name: 'Planetary Video',
            description: 'Capture high frame rate video',
            duration: 600, // 10 minutes
            enabled: true,
            retryCount: 0,
            maxRetries: 2,
            settings: {
              exposure: 0.01, // 10ms
              count: 30000, // 30k frames
              binning: '1x1',
              frameType: 'light',
              gain: 300,
            },
          },
          {
            type: 'wait',
            name: 'Cool Down',
            description: 'Allow camera to cool down',
            duration: 300,
            enabled: true,
            retryCount: 0,
            maxRetries: 1,
            settings: {
              duration: 300,
              reason: 'Camera cool down period',
            },
          },
          {
            type: 'capture',
            name: 'Second Video Run',
            description: 'Capture second video sequence',
            duration: 600,
            enabled: true,
            retryCount: 0,
            maxRetries: 2,
            settings: {
              exposure: 0.01,
              count: 30000,
              binning: '1x1',
              frameType: 'light',
              gain: 300,
            },
          },
        ],
      },
      {
        id: 'template-calibration',
        name: 'Calibration Frames',
        description: 'Complete set of calibration frames (darks, flats, bias)',
        category: 'Calibration',
        isBuiltIn: true,
        metadata: {
          tags: ['Calibration', 'Darks', 'Flats', 'Bias'],
          difficulty: 'beginner',
          equipment: ['Camera'],
          estimatedTime: 2 * 3600, // 2 hours
          targetType: 'calibration',
        },
        steps: [
          {
            type: 'calibration',
            name: 'Bias Frames',
            description: 'Capture bias calibration frames',
            duration: 300, // 5 minutes
            enabled: true,
            retryCount: 0,
            maxRetries: 2,
            settings: {
              frameType: 'bias',
              count: 50,
              binning: '1x1',
            },
          },
          {
            type: 'calibration',
            name: 'Dark Frames 300s',
            description: 'Capture 300s dark frames',
            duration: 6000, // 20 x 300s
            enabled: true,
            retryCount: 0,
            maxRetries: 2,
            settings: {
              frameType: 'dark',
              count: 20,
              exposure: 300,
              binning: '1x1',
              temperature: -10,
            },
          },
          {
            type: 'calibration',
            name: 'Flat Frames',
            description: 'Capture flat field frames',
            duration: 600, // 10 minutes
            enabled: true,
            retryCount: 0,
            maxRetries: 3,
            settings: {
              frameType: 'flat',
              count: 30,
              exposure: 1.0,
              binning: '1x1',
            },
          },
        ],
      },
    ];
  }

  static createSampleSequence(): Sequence {
    return {
      id: generateId(),
      name: 'M31 Deep Sky Sample',
      description: 'Sample LRGB sequence for the Andromeda Galaxy',
      target: 'M31 - Andromeda Galaxy',
      status: 'idle',
      progress: 0,
      currentStepIndex: -1,
      estimatedDuration: 12630, // Total duration in seconds
      conditions: [],
      metadata: {
        tags: ['Sample', 'M31', 'LRGB', 'Deep Sky'],
        category: 'Deep Sky',
        difficulty: 'intermediate',
        equipment: ['Camera', 'Filter Wheel', 'Mount', 'Focuser'],
        targetType: 'dso',
      },
      created: new Date(),
      modified: new Date(),
      version: '1.0.0',
      steps: [
        {
          id: generateId(),
          type: 'slew',
          name: 'Slew to M31',
          description: 'Point telescope at Andromeda Galaxy',
          duration: 30,
          enabled: true,
          retryCount: 0,
          maxRetries: 3,
          status: 'pending',
          progress: 0,
          settings: {
            ra: '00h 42m 44s',
            dec: '+41° 16\' 09"',
            targetName: 'M31 - Andromeda Galaxy',
            platesolve: true,
            centerTarget: true,
          },
        },
        {
          id: generateId(),
          type: 'focus',
          name: 'Auto Focus',
          description: 'Perform initial auto focus routine',
          duration: 60,
          enabled: true,
          retryCount: 0,
          maxRetries: 3,
          status: 'pending',
          progress: 0,
          settings: {
            type: 'auto',
            tolerance: 0.5,
            maxAttempts: 5,
            useTemperatureCompensation: true,
          },
        },
        {
          id: generateId(),
          type: 'filter',
          name: 'Set Luminance Filter',
          description: 'Change to luminance filter',
          duration: 10,
          enabled: true,
          retryCount: 0,
          maxRetries: 3,
          status: 'pending',
          progress: 0,
          settings: {
            position: 1,
            name: 'Luminance',
            waitTime: 5,
          },
        },
        {
          id: generateId(),
          type: 'capture',
          name: 'Luminance Frames',
          description: 'Capture 20 x 300s luminance frames',
          duration: 6000,
          enabled: true,
          retryCount: 0,
          maxRetries: 2,
          status: 'pending',
          progress: 0,
          settings: {
            exposure: 300,
            count: 20,
            binning: '1x1',
            frameType: 'light',
            dither: true,
            ditherPixels: 3,
          },
        },
        {
          id: generateId(),
          type: 'filter',
          name: 'Set Red Filter',
          description: 'Change to red filter',
          duration: 10,
          status: 'pending',
          progress: 0,
          enabled: true,
          retryCount: 0,
          maxRetries: 3,
          estimatedCompletion: new Date(Date.now() + 10000),
          settings: {
            position: 2,
            name: 'Red',
            waitTime: 5,
          },
        },
        {
          id: generateId(),
          type: 'capture',
          name: 'Red Frames',
          description: 'Capture 10 x 300s red frames',
          duration: 3000,
          status: 'pending',
          progress: 0,
          enabled: true,
          retryCount: 0,
          maxRetries: 2,
          estimatedCompletion: new Date(Date.now() + 3000000),
          settings: {
            exposure: 300,
            count: 10,
            binning: '1x1',
            frameType: 'light',
            dither: true,
            ditherPixels: 3,
          },
        },
        {
          id: generateId(),
          type: 'filter',
          name: 'Set Green Filter',
          description: 'Change to green filter',
          duration: 10,
          status: 'pending',
          progress: 0,
          enabled: true,
          retryCount: 0,
          maxRetries: 3,
          estimatedCompletion: new Date(Date.now() + 10000),
          settings: {
            position: 3,
            name: 'Green',
            waitTime: 5,
          },
        },
        {
          id: generateId(),
          type: 'capture',
          name: 'Green Frames',
          description: 'Capture 10 x 300s green frames',
          duration: 3000,
          status: 'pending',
          progress: 0,
          enabled: true,
          retryCount: 0,
          maxRetries: 2,
          estimatedCompletion: new Date(Date.now() + 3000000),
          settings: {
            exposure: 300,
            count: 10,
            binning: '1x1',
            frameType: 'light',
            dither: true,
            ditherPixels: 3,
          },
        },
        {
          id: generateId(),
          type: 'filter',
          name: 'Set Blue Filter',
          description: 'Change to blue filter',
          duration: 10,
          status: 'pending',
          progress: 0,
          enabled: true,
          retryCount: 0,
          maxRetries: 3,
          estimatedCompletion: new Date(Date.now() + 10000),
          settings: {
            position: 4,
            name: 'Blue',
            waitTime: 5,
          },
        },
        {
          id: generateId(),
          type: 'capture',
          name: 'Blue Frames',
          description: 'Capture 10 x 300s blue frames',
          duration: 3000,
          status: 'pending',
          progress: 0,
          enabled: true,
          retryCount: 0,
          maxRetries: 2,
          estimatedCompletion: new Date(Date.now() + 3000000),
          settings: {
            exposure: 300,
            count: 10,
            binning: '1x1',
            frameType: 'light',
            dither: true,
            ditherPixels: 3,
          },
        },
      ],
    };
  }
}
