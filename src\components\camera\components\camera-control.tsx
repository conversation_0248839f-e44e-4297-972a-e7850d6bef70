"use client";

import React, { useState, useCallback } from 'react';
// import { useTranslation } from "@/lib/i18n"; // Unused for now
import { useCamera } from "../hooks/use-camera";
import { useEnhancedInteractions, usePinchGesture, useSwipeGesture } from '@/hooks/use-enhanced-interactions';
import { useAccessibility } from '@/hooks/use-accessibility';
import { usePerformanceMonitor } from '@/lib/performance-monitor';
import { GestureState } from '@/lib/interaction-manager';
import CameraStatus from "./camera-status";
import ExposureSettings from "./exposure-settings";
import CameraSettings from "./camera-settings";
import CaptureControls from "./capture-controls";
import LiveView from "./live-view";

interface CameraControlProps {
  className?: string;
  layout?: "compact" | "full";
  enableEnhancedGestures?: boolean;
}

export default function CameraControl({
  className,
  layout = "full",
  enableEnhancedGestures = false
}: CameraControlProps) {
  const { cameraStatus, cameraSettings, updateCameraSettings } = useCamera();
  // const { t } = useTranslation(); // Unused for now

  const [gestureLog, setGestureLog] = useState<string[]>([]);
  const { announce } = useAccessibility();
  const { measureInteraction } = usePerformanceMonitor();

  // Enhanced gesture handlers (only active when enableEnhancedGestures is true)
  const { ref: zoomRef } = usePinchGesture(
    useCallback((gesture) => {
      if (!enableEnhancedGestures) return;
      const scale = (gesture as any).scale || 1;
      const currentZoom = (cameraSettings as any).zoom || 1;
      const newZoom = Math.max(1, Math.min(10, currentZoom * scale));
      updateCameraSettings({ zoom: newZoom } as any);
      announce(`Zoom: ${newZoom.toFixed(1)}x`);
    }, [enableEnhancedGestures, cameraSettings, updateCameraSettings, announce])
  );

  const { ref: swipeRef } = useSwipeGesture(
    useCallback((direction) => {
      if (!enableEnhancedGestures) return;
      const currentExposure = (cameraSettings as any).exposure || 0;
      const currentGain = cameraSettings.gain || 100;

      switch (direction) {
        case 'left':
          updateCameraSettings({
            exposure: Math.max(0.001, currentExposure * 0.5)
          } as any);
          announce('Exposure decreased');
          break;
        case 'right':
          updateCameraSettings({
            exposure: Math.min(300, currentExposure * 2)
          } as any);
          announce('Exposure increased');
          break;
        case 'up':
          updateCameraSettings({
            gain: Math.min(1000, currentGain * 1.5)
          });
          announce(`Gain increased to ${Math.round(currentGain * 1.5)}`);
          break;
        case 'down':
          updateCameraSettings({
            gain: Math.max(0, currentGain * 0.75)
          });
          announce(`Gain decreased to ${Math.round(currentGain * 0.75)}`);
          break;
      }
    }, [enableEnhancedGestures, cameraSettings, updateCameraSettings, announce])
  );

  const handleDownload = () => {
    // Implement download functionality
    console.log("Download requested");
  };

  const handleSaveFrame = () => {
    // Implement save frame functionality
    console.log("Save frame requested");
  };

  const handleFullscreen = () => {
    // Implement fullscreen functionality
    console.log("Fullscreen requested");
  };

  if (layout === "compact") {
    return (
      <div
        className={`space-y-4 ${className}`}
        ref={enableEnhancedGestures ? swipeRef as React.RefObject<HTMLDivElement> : undefined}
      >
        <CameraStatus compact />
        <CaptureControls
          layout="stack"
          showDownloadButton={false}
          onDownload={handleDownload}
        />
        {cameraStatus.liveViewActive && (
          <div ref={enableEnhancedGestures ? zoomRef as React.RefObject<HTMLDivElement> : undefined}>
            <LiveView
              aspectRatio="square"
              showControls={false}
              onFullscreen={handleFullscreen}
              onSaveFrame={handleSaveFrame}
            />
          </div>
        )}

        {/* Enhanced gesture feedback */}
        {enableEnhancedGestures && gestureLog.length > 0 && (
          <div className="text-xs text-muted-foreground p-2 bg-muted/50 rounded">
            Last gesture: {gestureLog[gestureLog.length - 1]}
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      className={`space-y-4 ${className}`}
      ref={enableEnhancedGestures ? swipeRef as React.RefObject<HTMLDivElement> : undefined}
    >
      {/* Camera Status */}
      <CameraStatus />

      {/* Exposure Settings */}
      <ExposureSettings />

      {/* Camera Settings */}
      <CameraSettings />

      {/* Capture Control */}
      <CaptureControls
        onDownload={handleDownload}
      />

      {/* Live View with enhanced gestures */}
      {cameraStatus.liveViewActive && (
        <div ref={enableEnhancedGestures ? zoomRef as React.RefObject<HTMLDivElement> : undefined}>
          <LiveView
            onFullscreen={handleFullscreen}
            onSaveFrame={handleSaveFrame}
          />
        </div>
      )}

      {/* Enhanced gesture feedback */}
      {enableEnhancedGestures && gestureLog.length > 0 && (
        <div className="text-xs text-muted-foreground p-2 bg-muted/50 rounded">
          Last gesture: {gestureLog[gestureLog.length - 1]}
        </div>
      )}
    </div>
  );
}
